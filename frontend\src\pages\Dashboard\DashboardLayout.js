import {
  FileText,
  X,
  Settings,
  Globe,
  MapPin,
  Building,
  Network,
  LogOut,
  ChevronDown,
  ChevronUp,
  CreditCard,
  Tag,
  Shield,
  Camera,
  UserCog,
  LayoutDashboard,
  Menu,
  Radio,
  Badge
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { NavLink, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/authContext';
import Header from '../../components/layout/Header';
import PermissionGuard from '../../components/auth/PermissionGuard';

const DashboardLayout = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    masterManagement: false,
    companyManagement: false,
    plazaManagement: false,
    laneManagement: false
  });
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Auto-expand sections based on current route
  useEffect(() => {
    const path = location.pathname;
    const newExpandedSections = { ...expandedSections };

    // Master Management section
    if (path.includes('/dashboard/manage-country') ||
        path.includes('/dashboard/manage-state') ||
        path.includes('/dashboard/manage-city')) {
      newExpandedSections.masterManagement = true;
    }

    // Company Management section
    if (path.includes('/dashboard/manage-company')) {
      newExpandedSections.companyManagement = true;
    }

    // Plaza Management section
    if (path.includes('/dashboard/manage-plaza')) {
      newExpandedSections.plazaManagement = true;
    }

    // Lane Management section
    if (path.includes('/dashboard/manage-lane') ||
        path.includes('/dashboard/manage-anpr') ||
        path.includes('/dashboard/manage-digital-pay') ||
        path.includes('/dashboard/manage-fastag') ||
        path.includes('/dashboard/manage-uhf-reader') ||
        path.includes('/dashboard/manage-pass-registration')) {
      newExpandedSections.laneManagement = true;
    }

    setExpandedSections(newExpandedSections);
  }, [location.pathname]);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Custom NavLink class function to handle active states
  const getNavLinkClass = ({ isActive }) => {
    return `flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${isActive
      ? 'bg-blue-100 text-blue-600 font-medium'
      : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'}`;
  };

  // Custom NavLink class function for submenu items
  const getSubNavLinkClass = ({ isActive }) => {
    return `flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${isActive
      ? 'bg-green-100 text-green-600 font-medium'
      : 'text-gray-600 hover:bg-green-50 hover:text-green-600'}`;
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row theme-transition theme-bg-secondary">
      {/* Overlay for mobile */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar - Fixed position with independent scrolling */}
      <aside
        className={`fixed top-0 left-0 z-50 h-full w-64 sm:w-72 md:w-60 lg:w-64 xl:w-72 theme-shadow transform ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 transition-transform duration-300 ease-in-out flex flex-col theme-bg-sidebar theme-transition`}
        style={{
          borderRight: '1px solid var(--color-border)'
        }}
      >
        {/* Sidebar Header - Fixed at top */}
        <div
          className="h-16 flex items-center justify-between px-4 z-10 flex-shrink-0 theme-bg-sidebar theme-transition"
          style={{
            borderBottom: '1px solid var(--color-border)'
          }}
        >
          <div className="flex items-center h-full">
            <NavLink to="/dashboard" className="flex items-center h-full py-2">
              <img 
                src="/images/Logo 2.jpg" 
                alt="Parkwiz Logo" 
                className="h-18 w-auto object-contain"
                style={{ maxWidth: '180px' }}
              />
            </NavLink>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="lg:hidden p-1.5 rounded-md transition-colors theme-bg-secondary theme-text-primary theme-transition"
            aria-label="Close menu"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Scrollable Navigation Area */}
        <div
          className="flex-1 overflow-y-auto scrollbar-thin theme-transition"
          style={{
            scrollbarColor: 'var(--color-text-muted) var(--color-bg-secondary)'
          }}
        >
          <nav className="p-3 space-y-3">
            {/* Main Navigation */}
            <div className="mb-5">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Main</p>
              <div className="space-y-1">
                <NavLink to="/dashboard" className={getNavLinkClass}>
                  <LayoutDashboard className="w-4 h-4" />
                  <span className="text-sm">Dashboard</span>
                </NavLink>

                <PermissionGuard requiredModule="Reports" requiredPermissions={["View"]}>
                  <NavLink to="/dashboard/reports" className={getNavLinkClass}>
                    <FileText className="w-4 h-4" />
                    <span className="text-sm">Reports</span>
                  </NavLink>
                </PermissionGuard>

                <PermissionGuard requiredModule="Users" requiredPermissions={["View"]}>
                  <NavLink to="/dashboard/manage-users" className={getNavLinkClass}>
                    <UserCog className="w-4 h-4" />
                    <span className="text-sm">User Management</span>
                  </NavLink>
                </PermissionGuard>
              </div>
            </div>

            {/* Master Management Section - Only visible to SuperAdmin */}
            <PermissionGuard allowedRoles={["SuperAdmin"]}>
              <div className="mb-5">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Location Management</p>
                <div className="space-y-1">
                  <button
                    onClick={() => toggleSection('masterManagement')}
                    className={`flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all duration-200 ${expandedSections.masterManagement ? 'bg-green-50 text-green-600' : ''}`}
                    aria-expanded={expandedSections.masterManagement}
                  >
                    <span className="flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      <span className="text-sm">Location Management</span>
                    </span>
                    {expandedSections.masterManagement ? (
                      <ChevronUp className="w-3.5 h-3.5 transition-transform duration-200" />
                    ) : (
                      <ChevronDown className="w-3.5 h-3.5 transition-transform duration-200" />
                    )}
                  </button>

                  {expandedSections.masterManagement && (
                    <div className="pl-3 space-y-1 mt-1 animate-fadeIn">
                      <PermissionGuard requiredModule="Countries" requiredPermissions={["View"]}>
                        <NavLink to="/dashboard/manage-country" className={getSubNavLinkClass}>
                          <Globe className="w-3.5 h-3.5" />
                          <span className="text-sm">Manage Country</span>
                        </NavLink>
                      </PermissionGuard>

                      <PermissionGuard requiredModule="States" requiredPermissions={["View"]}>
                        <NavLink to="/dashboard/manage-state" className={getSubNavLinkClass}>
                          <MapPin className="w-3.5 h-3.5" />
                          <span className="text-sm">Manage State</span>
                        </NavLink>
                      </PermissionGuard>

                      <PermissionGuard requiredModule="Cities" requiredPermissions={["View"]}>
                        <NavLink to="/dashboard/manage-city" className={getSubNavLinkClass}>
                          <MapPin className="w-3.5 h-3.5" />
                          <span className="text-sm">Manage City</span>
                        </NavLink>
                      </PermissionGuard>
                    </div>
                  )}
                </div>
              </div>
            </PermissionGuard>

            {/* Company Management Section - Visible to SuperAdmin and CompanyAdmin */}
            <PermissionGuard allowedRoles={["SuperAdmin", "CompanyAdmin"]}>
              <div className="mb-5">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Company</p>
                <div className="space-y-1">
                  <button
                    onClick={() => toggleSection('companyManagement')}
                    className={`flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all duration-200 ${expandedSections.companyManagement ? 'bg-green-50 text-green-600' : ''}`}
                    aria-expanded={expandedSections.companyManagement}
                  >
                    <span className="flex items-center gap-2">
                      <Building className="w-4 h-4" />
                      <span className="text-sm">Company Management</span>
                    </span>
                    {expandedSections.companyManagement ? (
                      <ChevronUp className="w-3.5 h-3.5 transition-transform duration-200" />
                    ) : (
                      <ChevronDown className="w-3.5 h-3.5 transition-transform duration-200" />
                    )}
                  </button>

                  {expandedSections.companyManagement && (
                    <div className="pl-3 space-y-1 mt-1 animate-fadeIn">
                      <PermissionGuard requiredModule="Companies" requiredPermissions={["View"]}>
                        <NavLink to="/dashboard/manage-company" className={getSubNavLinkClass}>
                          <Building className="w-3.5 h-3.5" />
                          <span className="text-sm">Manage Companies</span>
                        </NavLink>
                      </PermissionGuard>
                    </div>
                  )}
                </div>
              </div>
            </PermissionGuard>

            {/* Plaza Management Section */}
            <div className="mb-5">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Plaza</p>
              <div className="space-y-1">
                <button
                  onClick={() => toggleSection('plazaManagement')}
                  className={`flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all duration-200 ${expandedSections.plazaManagement ? 'bg-green-50 text-green-600' : ''}`}
                  aria-expanded={expandedSections.plazaManagement}
                >
                  <span className="flex items-center gap-2">
                    <Building className="w-4 h-4" />
                    <span className="text-sm">Plaza Management</span>
                  </span>
                  {expandedSections.plazaManagement ? (
                    <ChevronUp className="w-3.5 h-3.5 transition-transform duration-200" />
                  ) : (
                    <ChevronDown className="w-3.5 h-3.5 transition-transform duration-200" />
                  )}
                </button>

                {expandedSections.plazaManagement && (
                  <div className="pl-3 space-y-1 mt-1 animate-fadeIn">
                    <PermissionGuard requiredModule="Plazas" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-plaza" className={getSubNavLinkClass}>
                        <Building className="w-3.5 h-3.5" />
                        <span className="text-sm">Manage Plazas</span>
                      </NavLink>
                    </PermissionGuard>
                  </div>
                )}
              </div>
            </div>

            {/* Lane Management Section */}
            <div className="mb-5">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Lane</p>
              <div className="space-y-1">
                <button
                  onClick={() => toggleSection('laneManagement')}
                  className={`flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all duration-200 ${expandedSections.laneManagement ? 'bg-green-50 text-green-600' : ''}`}
                  aria-expanded={expandedSections.laneManagement}
                >
                  <span className="flex items-center gap-2">
                    <Network className="w-4 h-4" />
                    <span className="text-sm">Lane Management</span>
                  </span>
                  {expandedSections.laneManagement ? (
                    <ChevronUp className="w-3.5 h-3.5 transition-transform duration-200" />
                  ) : (
                    <ChevronDown className="w-3.5 h-3.5 transition-transform duration-200" />
                  )}
                </button>

                {expandedSections.laneManagement && (
                  <div className="pl-3 space-y-1 mt-1 animate-fadeIn">
                    <PermissionGuard requiredModule="Lanes" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-lane" className={getSubNavLinkClass}>
                        <Network className="w-3.5 h-3.5" />
                        <span className="text-sm">Manage Lanes</span>
                      </NavLink>
                    </PermissionGuard>

                    <PermissionGuard requiredModule="ANPR" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-anpr" className={getSubNavLinkClass}>
                        <Camera className="w-3.5 h-3.5" />
                        <span className="text-sm">ANPR Configuration</span>
                      </NavLink>
                    </PermissionGuard>

                    <PermissionGuard requiredModule="Digital Pay" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-digital-pay" className={getSubNavLinkClass}>
                        <CreditCard className="w-3.5 h-3.5" />
                        <span className="text-sm">Digital Payment</span>
                      </NavLink>
                    </PermissionGuard>

                    <PermissionGuard requiredModule="Fastag" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-fastag" className={getSubNavLinkClass}>
                        <Tag className="w-3.5 h-3.5" />
                        <span className="text-sm">Fastag</span>
                      </NavLink>
                    </PermissionGuard>

                    <PermissionGuard requiredModule="UHF Reader" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-uhf-reader" className={getSubNavLinkClass}>
                        <Radio className="w-3.5 h-3.5" />
                        <span className="text-sm">UHF Reader</span>
                      </NavLink>
                    </PermissionGuard>

                    <PermissionGuard requiredModule="Pass Registration" requiredPermissions={["View"]}>
                      <NavLink to="/dashboard/manage-pass-registration" className={getSubNavLinkClass}>
                        <Badge className="w-3.5 h-3.5" />
                        <span className="text-sm">Pass Registration</span>
                      </NavLink>
                    </PermissionGuard>
                  </div>
                )}
              </div>
            </div>

            {/* Settings */}
            <PermissionGuard
              allowedRoles={["SuperAdmin", "CompanyAdmin"]}
              fallback={null}
            >
              <div className="mb-5">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">System</p>
                <div className="space-y-1">
                  <PermissionGuard requiredModule="System Settings" requiredPermissions={["View"]}>
                    <NavLink to="/dashboard/settings" className={getNavLinkClass}>
                      <Settings className="w-4 h-4" />
                      <span className="text-sm">Settings</span>
                    </NavLink>
                  </PermissionGuard>

                  <PermissionGuard requiredModule="Security" requiredPermissions={["View"]}>
                    <NavLink to="/dashboard/security" className={getNavLinkClass}>
                      <Shield className="w-4 h-4" />
                      <span className="text-sm">Security</span>
                    </NavLink>
                  </PermissionGuard>
                </div>
              </div>
            </PermissionGuard>
          </nav>
        </div>

        {/* Sidebar Footer - Fixed at bottom */}
        <div className="p-3 border-t bg-white flex-shrink-0">
          <button
            onClick={() => {
              logout();
              navigate('/login');
            }}
            className="w-full flex items-center gap-2 px-3 py-2 text-red-600 rounded-lg hover:bg-red-50 transition-colors font-medium"
          >
            <LogOut className="w-4 h-4" />
            <span className="text-sm">Logout</span>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div
        className="flex-1 flex flex-col min-w-0 lg:ml-64 xl:ml-72 theme-bg-primary theme-transition"
        style={{ minHeight: '100vh' }}
      >
        {/* Header */}
        <Header toggleSidebar={() => setIsMobileMenuOpen(true)} />

        {/* Main Content Area */}
        <main
          className="flex-1 overflow-auto theme-bg-secondary theme-transition"
        >
          <div className="max-w-7xl mx-auto">
            <Outlet /> {/* Content changes here */}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;