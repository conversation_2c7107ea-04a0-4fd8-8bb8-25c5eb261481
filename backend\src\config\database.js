// backend/src/config/database.js - MODIFIED VERSION

const sql = require('mssql');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: 60000,    // Increased to 60 seconds
    requestTimeout: 60000,       // Increased to 60 seconds
    // Explicitly disable DAC usage
    admin: false
  },
  pool: {
    max: 20,                    // Increased pool size for better concurrency
    min: 5,                     // Keep minimum connections ready
    idleTimeoutMillis: 60000    // Increased idle timeout
  }
};

// Create a connection pool
let pool = null;

// Initialize the connection pool
const initializePool = async () => {
  try {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('Error closing existing pool (continuing anyway):', closeError.message);
        // Continue even if close fails
      }
    }
    
    pool = await sql.connect(dbConfig);

    // Test connection with a simple query
    const result = await pool.request().query(`SELECT DB_NAME() as DatabaseName`);

    if (result.recordset.length > 0) {
      const { DatabaseName } = result.recordset[0];
      console.log(`Database connected: ${DatabaseName}`);
      console.log(`Server: ${dbConfig.server}, User: ${dbConfig.user}`);
      
      // Check transaction isolation level and other session settings
      const sessionInfoResult = await pool.request().query(`
        SELECT 
          CASE transaction_isolation_level 
            WHEN 0 THEN 'Unspecified' 
            WHEN 1 THEN 'ReadUncommitted' 
            WHEN 2 THEN 'ReadCommitted' 
            WHEN 3 THEN 'Repeatable' 
            WHEN 4 THEN 'Serializable' 
            WHEN 5 THEN 'Snapshot' 
          END as IsolationLevel,
          @@SPID as SessionId,
          SUSER_NAME() as LoginName,
          DB_NAME() as DatabaseName,
          @@SERVERNAME as ServerName,
          @@VERSION as SqlServerVersion,
          GETDATE() as ServerTime,
          SYSDATETIMEOFFSET() as ServerTimeWithOffset
        FROM sys.dm_exec_sessions 
        WHERE session_id = @@SPID
      `);
      const sessionInfo = sessionInfoResult.recordset[0];
      console.log(`🔍 Database Session Info:`);
      console.log(`  - Isolation Level: ${sessionInfo.IsolationLevel}`);
      console.log(`  - Session ID: ${sessionInfo.SessionId}`);
      console.log(`  - Login Name: ${sessionInfo.LoginName}`);
      console.log(`  - Database: ${sessionInfo.DatabaseName}`);
      console.log(`  - Server: ${sessionInfo.ServerName}`);
      console.log(`  - Server Time: ${sessionInfo.ServerTime}`);
      console.log(`  - Server Time with Offset: ${sessionInfo.ServerTimeWithOffset}`);
    }
    
    return pool;
  } catch (error) {
    console.error('SQL Server connection pool error:', error);
    throw error;
  }
};

// Execute a query with retry logic
const query = async (queryText, params = {}, maxRetries = 3) => {
  if (!pool) {
    await initializePool();
  }
  
  let retries = maxRetries;
  
  while (retries >= 0) {
    try {
      const request = pool.request();
      
      // Set request timeout to 2 minutes for complex queries
      request.timeout = 120000; // 2 minutes
      
      // Add parameters to the request
      Object.entries(params).forEach(([key, value]) => {
        // Use VARCHAR for date parameters to avoid timezone conversion issues
        // This ensures exact string matching with SSMS queries
        if (key.includes('Date') && typeof value === 'string') {
          request.input(key, sql.VarChar(50), value);
        } else {
          request.input(key, value);
        }
      });
      
      // Execute the query
      const result = await request.query(queryText);
      return result;
    } catch (error) {
      console.error('Query execution error:', error.message);
      
      // If we have retries left, try again
      if (retries > 0) {
        console.log(`Retrying query, ${retries} attempts left`);
        retries--;

        // If connection pool error, reinitialize the pool
        if (error.code === 'ELOGIN' || error.code === 'ESOCKET') {
          if (pool) {
            try {
              await pool.close();
            } catch (closeError) {
              console.error('Error closing pool:', closeError.message);
            }
          }
          await initializePool();
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        throw error; // No more retries, throw the error
      }
    }
  }
};

// Close the connection pool
const closePool = async () => {
  if (pool) {
    try {
      await pool.close();
      pool = null;
    } catch (error) {
      console.error('Error closing database connection pool:', error);
      throw error;
    }
  }
};

// Initialize the pool when the module is imported
initializePool().catch(error => {
  console.error('Failed to initialize database connection pool:', error);
  process.exit(1); // Exit the process if initial connection fails
});

// Handle process termination
process.on('SIGINT', async () => {
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closePool();
  process.exit(0);
});

module.exports = {
  query,
  closePool,
  sql
};
