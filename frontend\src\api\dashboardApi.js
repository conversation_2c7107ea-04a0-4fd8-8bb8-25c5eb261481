import api from '../services/api';

/**
 * Dashboard API Service
 * 
 * Provides methods to interact with the dashboard endpoints
 */
const dashboardApi = {
  /**
   * Get dashboard summary metrics
   * 
   * @param {Object} params - Query parameters
   * @param {string} params.dateRange - Date range (today, yesterday, week, month, year)
   * @param {number} params.companyId - Optional company ID filter
   * @param {number} params.plazaId - Optional plaza ID filter
   * @param {number} params.laneId - Optional lane ID filter
   * @returns {Promise} Promise with dashboard summary data
   */
  getDashboardSummary: (params = {}) => {
    return api.get('/dashboard/summary', { params });
  },

  /**
   * Get revenue breakdown by payment method
   * 
   * @param {Object} params - Query parameters
   * @param {string} params.dateRange - Date range (today, yesterday, week, month, year)
   * @param {number} params.companyId - Optional company ID filter
   * @param {number} params.plazaId - Optional plaza ID filter
   * @returns {Promise} Promise with payment method revenue data
   */
  getRevenueByPaymentMethod: (params = {}) => {
    return api.get('/dashboard/revenue/by-payment-method', { params });
  },

  /**
   * Get recent transactions
   * 
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of transactions to return (default: 5)
   * @param {number} params.companyId - Optional company ID filter
   * @param {number} params.plazaId - Optional plaza ID filter
   * @returns {Promise} Promise with recent transactions data
   */
  getRecentTransactions: (params = {}) => {
    return api.get('/dashboard/transactions/recent', { params });
  },

  /**
   * Get transaction counts by hour of day
   *
   * @param {Object} params - Query parameters
   * @param {string} params.dateRange - Date range (today, yesterday, week, month, year)
   * @param {number} params.companyId - Optional company ID filter
   * @param {number} params.plazaId - Optional plaza ID filter
   * @returns {Promise} Promise with hourly transaction data
   */
  getPeakHoursData: (params = {}) => {
    return api.get('/dashboard/transactions/peak-hours', { params });
  },

  /**
   * Get daily revenue data for transaction overview chart
   *
   * @param {Object} params - Query parameters
   * @param {string} params.dateRange - Date range (today, yesterday, week, month, year)
   * @param {number} params.companyId - Optional company ID filter
   * @param {number} params.plazaId - Optional plaza ID filter
   * @returns {Promise} Promise with daily revenue data
   */
  getDailyRevenueData: (params = {}) => {
    return api.get('/dashboard/revenue/daily', { params });
  },

  /**
   * Get revenue breakdown by plaza
   * Only accessible to SuperAdmin and CompanyAdmin roles
   *
   * @param {Object} params - Query parameters
   * @param {string} params.dateRange - Date range (today, yesterday, week, month, year)
   * @param {number} params.companyId - Optional company ID filter
   * @returns {Promise} Promise with plaza revenue data
   */
  getRevenueByPlaza: (params = {}) => {
    return api.get('/dashboard/revenue/by-plaza', { params });
  },

  /**
   * Get lane status for a specific plaza
   * 
   * @param {Object} params - Query parameters
   * @param {number} params.plazaId - Plaza ID
   * @returns {Promise} Promise with lane status data
   */
  getLaneStatus: (params = {}) => {
    return api.get('/dashboard/lanes/status', { params });
  }
};

export default dashboardApi;