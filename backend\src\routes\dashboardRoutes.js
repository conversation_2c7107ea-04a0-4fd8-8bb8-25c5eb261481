const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/DashboardController');
const auth = require('../middleware/auth');

/**
 * ===============================================================================
 * # Dashboard Routes
 * ===============================================================================
 *
 * API routes for dashboard metrics and visualizations.
 * All routes require authentication.
 */

/**
 * @route GET /api/dashboard/summary
 * @desc Get dashboard summary metrics based on user role
 * @access Private
 */
router.get('/summary', auth(), dashboardController.getDashboardSummary);

/**
 * @route GET /api/dashboard/revenue/by-payment-method
 * @desc Get revenue breakdown by payment method
 * @access Private
 */
router.get('/revenue/by-payment-method', auth(), dashboardController.getRevenueByPaymentMethod);

/**
 * @route GET /api/dashboard/transactions/recent
 * @desc Get recent transactions
 * @access Private
 */
router.get('/transactions/recent', auth(), dashboardController.getRecentTransactions);

/**
 * @route GET /api/dashboard/transactions/peak-hours
 * @desc Get transaction counts by hour of day
 * @access Private
 */
router.get('/transactions/peak-hours', auth(), dashboardController.getPeakHoursData);

/**
 * @route GET /api/dashboard/revenue/daily
 * @desc Get daily revenue data for transaction overview chart
 * @access Private
 */
router.get('/revenue/daily', auth(), dashboardController.getDailyRevenueData);

/**
 * @route GET /api/dashboard/revenue/by-plaza
 * @desc Get revenue breakdown by plaza
 * @access Private (SuperAdmin, CompanyAdmin)
 */
router.get('/revenue/by-plaza', auth(), dashboardController.getRevenueByPlaza);

/**
 * @route GET /api/dashboard/lanes/status
 * @desc Get lane status for a specific plaza
 * @access Private
 */
router.get('/lanes/status', auth(), dashboardController.getLaneStatus);

/**
 * @route GET /api/dashboard/debug
 * @desc Debug endpoint to test queries and compare with SSMS
 * @access Private
 */
router.get('/debug', auth(), dashboardController.debugQuery);

module.exports = router;