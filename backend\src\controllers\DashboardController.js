const db = require('../config/database');
const sql = require('mssql');
const redisService = require('../services/RedisService');

/**
 * ===============================================================================
 * # Dashboard Controller - Enhanced Documentation
 * ===============================================================================
 *
 * This controller provides comprehensive dashboard endpoints for the PWVMS parking
 * and toll management system. It implements advanced caching, role-based access
 * control, and optimized database queries for real-time analytics.
 *
 * ## Key Features:
 * - **Multi-level Caching**: Redis-based caching with dynamic TTL based on data freshness
 * - **Role-based Access Control**: Different data access for SuperAdmin, CompanyAdmin, PlazaManager
 * - **Operational Day Logic**: 6:00 AM to 6:00 AM operational day calculations
 * - **Query Optimization**: Performance-optimized SQL queries with NOLOCK hints
 * - **Date Range Limiting**: Automatic query optimization for large date ranges (90-day limit)
 * - **Real-time Data**: Live parking data, lane status, and occupancy tracking
 *
 * ## Data Sources:
 * - **Primary Table**: `tblParkwiz_Parking_Data` - Main parking transaction data
 * - **Supporting Tables**: `Plaza`, `tblLaneDetails`, `UserCompany`, `UserPlaza`
 * - **Cache Layer**: Redis with structured key patterns and TTL management
 *
 * ## Cache Strategy:
 * - **Dashboard Summary**: 5 minutes TTL (300s)
 * - **Chart Data**: 10 minutes TTL (600s) 
 * - **Live Data**: 30 seconds TTL
 * - **Dynamic TTL**: Based on date range (today=1min, yesterday=5min, week=10min, etc.)
 *
 * ## Role-based Data Access:
 * - **SuperAdmin**: Full system access, all companies and plazas
 * - **CompanyAdmin**: Access to assigned companies and their plazas
 * - **PlazaManager**: Access to specific assigned plazas only
 *
 * ## Operational Day Logic:
 * The system uses 6:00 AM as the operational day boundary:
 * - Before 6:00 AM: Current operational day started yesterday at 6:00 AM
 * - After 6:00 AM: Current operational day started today at 6:00 AM
 * - This ensures consistent daily reporting regardless of when queries are run
 *
 * ## Performance Optimizations:
 * - **NOLOCK Hints**: Used for read operations to prevent blocking
 * - **MAXDOP 2**: Limits parallelism to prevent resource contention
 * - **Date Range Limiting**: Large queries automatically limited to 90 days
 * - **Indexed Queries**: Optimized WHERE clauses for efficient index usage
 * - **Single Query Aggregation**: Multiple metrics calculated in single database call
 *
 * ## Data Flow Architecture:
 * ```
 * Frontend Request → Controller → Redis Cache Check → Database Query → Cache Store → Response
 *                                      ↓ (Cache Hit)
 *                                 Cached Response
 * ```
 *
 * ## Available Endpoints:
 * 1. **GET /dashboard/summary** - Main dashboard metrics with vehicle counts and revenue
 * 2. **GET /dashboard/revenue-by-payment** - Payment method breakdown for pie charts
 * 3. **GET /dashboard/recent-transactions** - Latest transaction list for activity feed
 * 4. **GET /dashboard/peak-hours** - Hourly transaction distribution for bar charts
 * 5. **GET /dashboard/daily-revenue** - Daily revenue trends for line charts
 * 6. **GET /dashboard/revenue-by-plaza** - Plaza-wise revenue comparison
 * 7. **GET /dashboard/lane-status** - Real-time lane operational status
 * 8. **GET /dashboard/debug-query** - Development/debugging endpoint
 *
 * ## Cache Key Patterns:
 * - Dashboard Summary: `pwvms:dashboard:summary:{role}:{userId}:{filters}`
 * - Payment Revenue: `pwvms:dashboard:revenue:payment:{role}:{userId}:{filters}`
 * - Peak Hours: `pwvms:dashboard:peak:{role}:{userId}:{filters}`
 * - Recent Transactions: `pwvms:dashboard:transactions:{role}:{userId}:{filters}`
 *
 * ## Database Performance Features:
 * - **Query Hints**: WITH (NOLOCK) for read operations
 * - **Parallelism Control**: OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
 * - **Date Range Optimization**: Automatic 90-day limit for large queries
 * - **Single Query Aggregation**: Multiple metrics in one database call
 * - **Index-Optimized Filters**: WHERE clauses designed for index efficiency
 *
 * @module DashboardController
 * @version 2.0.0
 * <AUTHOR> Development Team
 * @since 2024-01-01
 */

/**
 * ===============================================================================
 * ## EXACT QUERY GENERATOR - Debug Query with Substituted Parameters
 * ===============================================================================
 * 
 * Generates the exact SQL query with all parameters substituted for debugging purposes.
 * This helps identify exactly what query is being executed against the database.
 * 
 * @param {string} queryTemplate - The SQL query template with @parameter placeholders
 * @param {Object} queryParams - The parameters object with values to substitute
 * @returns {string} The exact SQL query with all parameters substituted
 */
function generateExactQuery(queryTemplate, queryParams) {
  let exactQuery = queryTemplate;
  
  Object.keys(queryParams).forEach(key => {
    const value = queryParams[key];
    const paramPlaceholder = `@${key}`;
    
    // Handle different data types appropriately
    let replacementValue;
    if (value === null || value === undefined) {
      replacementValue = 'NULL';
    } else if (typeof value === 'string') {
      // Escape single quotes in strings and wrap in quotes
      replacementValue = `'${value.replace(/'/g, "''")}'`;
    } else if (typeof value === 'boolean') {
      replacementValue = value ? '1' : '0';
    } else if (typeof value === 'number') {
      replacementValue = value.toString();
    } else {
      // For other types, convert to string and wrap in quotes
      replacementValue = `'${String(value).replace(/'/g, "''")}'`;
    }
    
    // Replace all occurrences of the parameter placeholder
    exactQuery = exactQuery.replace(new RegExp(paramPlaceholder, 'g'), replacementValue);
  });
  
  return exactQuery;
}

/**
 * ===============================================================================
 * ## CACHE TTL STRATEGY - Dynamic Time-to-Live Management
 * ===============================================================================
 * 
 * Calculates appropriate cache TTL based on data freshness requirements.
 * More recent data has shorter TTL for better real-time accuracy, while
 * historical data can be cached longer for better performance.
 * 
 * ### TTL Strategy:
 * - **Today**: 60 seconds (1 minute) - Most dynamic, frequent updates needed
 * - **Yesterday**: 300 seconds (5 minutes) - Semi-static, moderate caching
 * - **Week**: 600 seconds (10 minutes) - Historical, longer caching acceptable
 * - **Month**: 1800 seconds (30 minutes) - Historical, extended caching
 * - **Year**: 3600 seconds (1 hour) - Historical, maximum caching
 * - **Default**: 300 seconds (5 minutes) - Safe fallback
 * 
 * @param {string} dateRange - The date range selection
 * @returns {number} TTL in seconds
 */
function getCacheTTL(dateRange) {
  switch(dateRange) {
    case 'today':
      return 60; // 1 minute for today's data
    case 'yesterday':
      return 300; // 5 minutes for yesterday
    case 'week':
      return 600; // 10 minutes for week
    case 'month':
      return 1800; // 30 minutes for month
    case 'year':
      return 3600; // 1 hour for year
    default:
      return 300; // 5 minutes default
  }
}

/**
 * Helper function to format date for SQL (YYYY-MM-DD HH:mm:ss)
 */
function formatDateForSQL(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * ===============================================================================
 * ## OPERATIONAL DAY CALCULATOR - 6:00 AM Boundary Logic
 * ===============================================================================
 * 
 * **Core business logic function** that calculates date ranges based on the
 * parking industry standard of 6:00 AM operational day boundaries. This ensures
 * consistent daily reporting regardless of when queries are executed.
 * 
 * ### Operational Day Concept:
 * - **Operational Day**: 6:00 AM to 6:00 AM (next day)
 * - **Before 6:00 AM**: Current operational day started yesterday at 6:00 AM
 * - **After 6:00 AM**: Current operational day started today at 6:00 AM
 * 
 * ### Supported Date Ranges:
 * - **'today'**: Current operational day (6:00 AM boundary logic)
 * - **'yesterday'**: Previous operational day
 * - **'week'**: Last 7 operational days
 * - **'month'**: Last 30 operational days  
 * - **'year'**: Last 365 operational days
 * - **'YYYY-MM-DD'**: Specific operational day (6:00 AM to 6:00 AM next day)
 * 
 * ### Return Object Structure:
 * ```javascript
 * {
 *   startDate: Date,           // JavaScript Date object
 *   endDate: Date,             // JavaScript Date object  
 *   startDateSQL: string,      // SQL formatted string
 *   endDateSQL: string,        // SQL formatted string
 *   sqlDateRange: string       // Ready-to-use SQL BETWEEN clause
 * }
 * ```
 * 
 * ### Examples:
 * - Query at 3:00 AM on Jan 15: "today" = Jan 14 6:00 AM to Jan 15 6:00 AM
 * - Query at 9:00 AM on Jan 15: "today" = Jan 15 6:00 AM to Jan 16 6:00 AM
 * - Specific date "2024-01-15": Jan 15 6:00 AM to Jan 16 6:00 AM
 * 
 * @param {string} dateRange - The date range selection (today, yesterday, week, month, year, or specific date in YYYY-MM-DD format)
 * @returns {Object} Object with startDate, endDate, and SQL-formatted strings
 */
function calculateDateRange(dateRange) {
  // Helper function to create date with 6:00 AM in local timezone
  const createLocalDate = (dateStr) => {
    // Parse the date string and create a date object at 6:00 AM local time
    const [year, month, day] = dateStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 6, 0, 0, 0);
    return date;
  };
  
  // Helper function to format date for SQL (YYYY-MM-DD HH:mm:ss)
  const formatForSQL = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };
  
  // Helper function to get date string (YYYY-MM-DD) from a date
  const getDateStr = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  // Helper function to add days to a date string
  const addDays = (dateStr, days) => {
    const date = new Date(dateStr + 'T00:00:00');
    date.setDate(date.getDate() + days);
    return getDateStr(date);
  };
  
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date - operational day from 6:00 AM to 6:00 AM next day
    startDate = createLocalDate(dateRange);
    endDate = createLocalDate(addDays(dateRange, 1));
    

  } else {
    // It's a predefined range - operational day logic (6:00 AM to 6:00 AM)
    const today = getDateStr(referenceDate);
    const currentHour = referenceDate.getHours();
    const isAfter6AM = currentHour >= 6;
    
    switch(dateRange) {
      case 'today':
        // Current operational day
        if (isAfter6AM) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = createLocalDate(today);
          endDate = createLocalDate(addDays(today, 1));
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          const yesterday = addDays(today, -1);
          startDate = createLocalDate(yesterday);
          endDate = createLocalDate(today);
        }
        break;
      case 'yesterday':
        // Previous operational day
        if (isAfter6AM) {
          // After 6 AM - yesterday's operational day
          const yesterday = addDays(today, -1);
          startDate = createLocalDate(yesterday);
          endDate = createLocalDate(today);
        } else {
          // Before 6 AM - day before yesterday's operational day
          const dayBeforeYesterday = addDays(today, -2);
          const yesterday = addDays(today, -1);
          startDate = createLocalDate(dayBeforeYesterday);
          endDate = createLocalDate(yesterday);
        }
        break;
      case 'week':
        // Last 7 operational days
        if (isAfter6AM) {
          // After 6 AM - include current operational day
          endDate = createLocalDate(addDays(today, 1));
        } else {
          // Before 6 AM - end at current operational day
          endDate = createLocalDate(today);
        }
        const endDateStr = getDateStr(endDate);
        startDate = createLocalDate(addDays(endDateStr, -7));
        break;
      case 'month':
        // Last 30 operational days
        if (isAfter6AM) {
          // After 6 AM - include current operational day
          endDate = createLocalDate(addDays(today, 1));
        } else {
          // Before 6 AM - end at current operational day
          endDate = createLocalDate(today);
        }
        const endDateStrMonth = getDateStr(endDate);
        startDate = createLocalDate(addDays(endDateStrMonth, -30));
        break;
      case 'year':
        // Last 365 operational days
        if (isAfter6AM) {
          // After 6 AM - include current operational day
          endDate = createLocalDate(addDays(today, 1));
        } else {
          // Before 6 AM - end at current operational day
          endDate = createLocalDate(today);
        }
        const endDateStrYear = getDateStr(endDate);
        startDate = createLocalDate(addDays(endDateStrYear, -365));
        break;
      default:
        // Default to current operational day
        if (isAfter6AM) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = createLocalDate(today);
          endDate = createLocalDate(addDays(today, 1));
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          const yesterday = addDays(today, -1);
          startDate = createLocalDate(yesterday);
          endDate = createLocalDate(today);
        }
    }
  }
  
  // Format for console output and SQL usage
  const startDateSQL = formatForSQL(startDate);
  const endDateSQL = formatForSQL(endDate);
  

  
  return { 
    startDate, 
    endDate,
    startDateSQL,
    endDateSQL,
    // For direct SQL usage
    sqlDateRange: `'${startDateSQL}' AND '${endDateSQL}'`
  };
}

/**
 * Helper function to format date for SQL (YYYY-MM-DD HH:mm:ss)
 */
function formatDateForSQL(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const dashboardController = {
  /**
   * ===============================================================================
   * ## GET DASHBOARD SUMMARY - Enhanced Analytics Endpoint
   * ===============================================================================
   *
   * **Primary dashboard endpoint** that provides comprehensive parking metrics with
   * intelligent caching and role-based data filtering. This is the most frequently
   * called endpoint and is heavily optimized for performance.
   *
   * ### Request Parameters:
   * - `dateRange` (string): 'today', 'yesterday', 'week', 'month', 'year', or 'YYYY-MM-DD'
   * - `companyId` (optional): Filter by specific company
   * - `plazaId` (optional): Filter by specific plaza
   * - `laneId` (optional): Filter by specific lane
   *
   * ### Response Data Structure:
   * ```json
   * {
   *   "success": true,
   *   "data": {
   *     "totalRevenue": 15000.50,
   *     "fourWheeler": {
   *       "revenue": 12000.00,
   *       "entryCount": 150,
   *       "exitCount": 145,
   *       "remainingCount": 5
   *     },
   *     "twoWheeler": {
   *       "revenue": 3000.50,
   *       "entryCount": 200,
   *       "exitCount": 195,
   *       "remainingCount": 5
   *     },
   *     "totalCounts": {
   *       "entryCount": 350,
   *       "exitCount": 340,
   *       "remainingCount": 10
   *     },
   *     "meta": {
   *       "dateRangeOptimized": false,
   *       "originalDateRange": {...},
   *       "queryDateRange": {...}
   *     }
   *   },
   *   "cached": false,
   *   "message": "Dashboard summary retrieved successfully"
   * }
   * ```
   *
   * ### Caching Strategy:
   * - **Cache Key Pattern**: `dashboard:summary:{role}:{userId}:{filters}`
   * - **TTL Logic**: Dynamic based on date range (1min for today, 5min for yesterday, etc.)
   * - **Cache Hit**: Returns cached data immediately with `cached: true`
   * - **Cache Miss**: Executes database query and caches result
   *
   * ### Performance Optimizations:
   * - **Single Query**: All metrics calculated in one optimized SQL query
   * - **Date Range Limiting**: Queries > 90 days automatically limited for performance
   * - **Role-based Filtering**: Applied at SQL level to reduce data transfer
   * - **Index Optimization**: WHERE clauses designed for optimal index usage
   *
   * ### Role-based Access:
   * - **SuperAdmin**: Access to all data across all companies/plazas
   * - **CompanyAdmin**: Filtered to user's assigned companies via UserCompany table
   * - **PlazaManager**: Filtered to user's assigned plazas via UserPlaza table
   *
   * ### Database Tables Used:
   * - `tblParkwiz_Parking_Data` (main data source)
   * - `Plaza` (for company/plaza filtering)
   * - `UserCompany` (CompanyAdmin role filtering)
   * - `UserPlaza` (PlazaManager role filtering)
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with comprehensive dashboard metrics
   */
  getDashboardSummary: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId, laneId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId, laneId };
      
      // Check Redis cache first
      const cachedData = await redisService.getDashboardSummary(userId, role, filters);
      if (cachedData) {

        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }
      
      // Cache miss - fetching from database
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Limit date range for large queries to improve performance
      let optimizedStartDate = startDate;
      let optimizedEndDate = endDate;
      
      // If date range is more than 90 days, limit to 90 days for better performance
      const maxRangeDays = 90; // 3 months max
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        // Create new date for optimization, maintaining the 6:00 AM time
        const optimizedEndDateStr = optimizedEndDate.toISOString().substring(0, 10);
        const tempStartDate = new Date(optimizedEndDateStr + 'T00:00:00');
        tempStartDate.setDate(tempStartDate.getDate() - maxRangeDays);
        const startDateStr = tempStartDate.toISOString().substring(0, 10);
        optimizedStartDate = new Date(startDateStr + 'T06:00:00');
      }
      
      // Base query parameters - convert dates to SQL format strings
      const queryParams = { 
        startDate: formatDateForSQL(optimizedStartDate), 
        endDate: formatDateForSQL(optimizedEndDate)
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      let laneFilter = '';
      if (laneId) {
        laneFilter = 'AND (t.EntryLane = @laneId OR t.ExitLane = @laneId)';
        queryParams.laneId = laneId;
      }

      // ===============================================================================
      // ## OPTIMIZED DASHBOARD SUMMARY QUERY
      // ===============================================================================
      // Single aggregated query that calculates all dashboard metrics in one database call.
      // Uses CASE statements for conditional aggregation to avoid multiple queries.
      // Performance optimized with NOLOCK hints and MAXDOP limitation.
      
      const summaryQuery = `
        SELECT 
          -- ===== REVENUE CALCULATIONS =====
          -- Total Revenue: Only from completed transactions (ExitDateTime not null)
          -- Uses ISNULL to handle NULL values and ensure numeric results
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- ===== FOUR WHEELER METRICS =====
          -- Revenue: Sum of parking fees for non-two-wheeler vehicles
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          -- Entry Count: Count of vehicles that entered during the date range
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          -- Exit Count: Count of vehicles that exited during the date range
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- ===== TWO WHEELER METRICS =====
          -- Revenue: Sum of parking fees for two-wheeler vehicles only
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          -- Entry Count: Two-wheeler entries in date range
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          -- Exit Count: Two-wheeler exits in date range
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- ===== TOTAL COUNTS =====
          -- Total Entry Count: All vehicles that entered during date range
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          -- Total Exit Count: All vehicles that exited during date range
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)  -- NOLOCK: Prevent read blocking
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        ${companyFilter}  -- Dynamic company filtering based on user role
        ${plazaFilter}    -- Dynamic plaza filtering based on user role
        ${laneFilter}     -- Optional lane-specific filtering
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)  -- Performance: Limit parallelism to 2 cores
      `;
      
      // Execute the optimized summary query
      
      // Build the complete query by resolving template literals first
      const completeQuery = `
        SELECT 
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      // Execute the optimized summary query with proper parameter binding
      const summaryResult = await db.query(summaryQuery, queryParams);
      
      // Log the row count for verification
      if (summaryResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No data found for the specified criteria'
        });
      }
      
      // Process the query results
      const data = summaryResult.recordset[0];
      
      // Calculate remaining vehicle counts (entries - exits)
      const fourWheelerRemaining = (data.FourWheelerEntryCount || 0) - (data.FourWheelerExitCount || 0);
      const twoWheelerRemaining = (data.TwoWheelerEntryCount || 0) - (data.TwoWheelerExitCount || 0);
      const totalRemaining = (data.TotalEntryCount || 0) - (data.TotalExitCount || 0);
      // Build the response object with calculated data
      const response = {
        // Total Revenue
        totalRevenue: data.TotalRevenue || 0,
        
        // Four Wheeler Data
        fourWheeler: {
          revenue: data.FourWheelerRevenue || 0,
          entryCount: data.FourWheelerEntryCount || 0,
          exitCount: data.FourWheelerExitCount || 0,
          remainingCount: Math.max(0, fourWheelerRemaining) // Ensure non-negative
        },
        
        // Two Wheeler Data
        twoWheeler: {
          revenue: data.TwoWheelerRevenue || 0,
          entryCount: data.TwoWheelerEntryCount || 0,
          exitCount: data.TwoWheelerExitCount || 0,
          remainingCount: Math.max(0, twoWheelerRemaining) // Ensure non-negative
        },
        
        // Total Counts
        totalCounts: {
          entryCount: data.TotalEntryCount || 0,
          exitCount: data.TotalExitCount || 0,
          remainingCount: Math.max(0, totalRemaining) // Ensure non-negative
        },
        
        // Add metadata about query optimization
        meta: {
          dateRangeOptimized: requestedRangeDays > maxRangeDays,
          originalDateRange: {
            start: startDate,
            end: endDate
          },
          queryDateRange: {
            start: optimizedStartDate,
            end: optimizedEndDate
          }
        }
      };
      
      // Cache the results with appropriate TTL based on date range
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheDashboardSummary(userId, role, filters, response);
      
      return res.status(200).json({
        success: true,
        data: response,
        cached: false,
        message: 'Dashboard summary retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getDashboardSummary:', error.message);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve dashboard summary',
        error: error.message,
        errorCode: error.code
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PAYMENT METHOD - Payment Analytics
   * ===============================================================================
   *
   * **Payment method analytics endpoint** that provides detailed breakdown of revenue
   * by payment type (Cash, Card, UPI, Fastag, etc.) with transaction counts and
   * intelligent caching for dashboard charts.
   *
   * ### Request Parameters:
   * - `dateRange` (string): 'today', 'yesterday', 'week', 'month', 'year', or 'YYYY-MM-DD'
   * - `companyId` (optional): Filter by specific company
   * - `plazaId` (optional): Filter by specific plaza
   *
   * ### Response Data Structure:
   * ```json
   * {
   *   "success": true,
   *   "data": [
   *     {
   *       "paymentMode": "Fastag",
   *       "totalRevenue": 8500.75,
   *       "transactionCount": 125
   *     },
   *     {
   *       "paymentMode": "Cash",
   *       "totalRevenue": 4200.25,
   *       "transactionCount": 85
   *     },
   *     {
   *       "paymentMode": "UPI",
   *       "totalRevenue": 2300.00,
   *       "transactionCount": 45
   *     }
   *   ],
   *   "cached": false,
   *   "message": "Revenue by payment method retrieved successfully"
   * }
   * ```
   *
   * ### Key Features:
   * - **Revenue Calculation**: Includes ParkingFee + iTotalGSTFee for complete revenue
   * - **Sorted Results**: Ordered by totalRevenue DESC for better visualization
   * - **Unknown Handling**: NULL payment modes displayed as 'Unknown'
   * - **Chart Optimization**: Data structure optimized for pie/donut charts
   *
   * ### Caching Strategy:
   * - **Cache Key Pattern**: `dashboard:revenue:payment:{role}:{userId}:{filters}`
   * - **TTL**: 10 minutes (DASHBOARD_CHARTS TTL)
   * - **Invalidation**: Automatic on new transaction data
   *
   * ### SQL Query Details:
   * - **Aggregation**: SUM(ParkingFee + iTotalGSTFee) grouped by PaymentMode
   * - **Date Filter**: Only ExitDateTime transactions (completed transactions)
   * - **Performance**: Uses NOLOCK hints and MAXDOP 2 for optimization
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with payment method revenue breakdown
   */
  getRevenueByPaymentMethod: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId };
      
      // Check Redis cache first
      const cachedData = await redisService.getRevenueByPayment(userId, role, filters);
      if (cachedData) {

        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          message: 'Revenue by payment method retrieved successfully'
        });
      }
      
      // Cache miss - fetching from database
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Limit date range for large queries to improve performance (same as Dashboard Summary)
      let optimizedStartDate = startDate;
      let optimizedEndDate = endDate;
      
      // If date range is more than 90 days, limit to 90 days for better performance
      const maxRangeDays = 90; // 3 months max
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        // Create new date for optimization, maintaining the 6:00 AM time
        const optimizedEndDateStr = optimizedEndDate.toISOString().substring(0, 10);
        const tempStartDate = new Date(optimizedEndDateStr + 'T00:00:00');
        tempStartDate.setDate(tempStartDate.getDate() - maxRangeDays);
        const startDateStr = tempStartDate.toISOString().substring(0, 10);
        optimizedStartDate = new Date(startDateStr + 'T06:00:00');
        

      }
      
      // Base query parameters
      const queryParams = { 
        startDate: formatDateForSQL(optimizedStartDate), 
        endDate: formatDateForSQL(optimizedEndDate)
      };
      

      console.log('  - Date Range Days:', requestedRangeDays);
      console.log('  - Using 6:00 AM to 6:00 AM operational day logic');
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      console.log('🔐 Applying Role-based Filtering:');
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
        console.log('  - CompanyAdmin filter applied for userId:', userId);
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
        console.log('  - PlazaManager filter applied for userId:', userId);
      } else {
        console.log('  - SuperAdmin: No role-based filtering');
      }
      
      // Entity-specific filtering
      console.log('🏢 Applying Entity-specific Filtering:');
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
        console.log('  - Company filter applied for companyId:', companyId);
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
        console.log('  - Plaza filter applied for plazaId:', plazaId);
      }
      
      console.log('📋 Final Query Parameters:', queryParams);
      console.log('📋 Final Filters Applied:');
      console.log('  - Company Filter:', companyFilter || 'None');
      console.log('  - Plaza Filter:', plazaFilter || 'None');
      
      // Execute optimized payment method query with enhanced debugging
      const paymentMethodQuery = `
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          -- Revenue Calculation: ParkingFee only (no GST)
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalRevenue,
          -- Individual components for debugging
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalParkingFee,
          COUNT(*) as transactionCount,
          -- Sample values for debugging
          AVG(ISNULL(t.ParkingFee, 0)) as avgParkingFee,
          MIN(ISNULL(t.ParkingFee, 0)) as minRevenue,
          MAX(ISNULL(t.ParkingFee, 0)) as maxRevenue
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      console.log('🎯 Executing Payment Method Query:');
      console.log('📝 SQL Query Template:', paymentMethodQuery);
      
      // Generate the exact query with parameters substituted for debugging
      const exactQuery = generateExactQuery(paymentMethodQuery, queryParams);
      
      console.log('🔍 EXACT PAYMENT METHOD SQL QUERY WITH VALUES:');
      console.log('===============================================');
      console.log(exactQuery);
      console.log('===============================================');
      console.log('📋 COPY-PASTE READY FOR SSMS:');
      console.log('------------------------------');
      console.log(exactQuery.replace(/\s+/g, ' ').trim());
      console.log('------------------------------');
      
      // Also log parameters separately for clarity
      console.log('📋 Query Parameters:');
      Object.keys(queryParams).forEach(key => {
        console.log(`  @${key} = ${queryParams[key]} (${typeof queryParams[key]})`);
      });
      
      // Log query execution timing
      const queryStartTime = Date.now();
      console.log('⏱️ Query execution started at:', new Date().toISOString());
      
      const result = await db.query(paymentMethodQuery, queryParams);
      
      const queryEndTime = Date.now();
      const queryDuration = queryEndTime - queryStartTime;
      console.log('⏱️ Query execution completed at:', new Date().toISOString());
      console.log('⏱️ Query execution time:', queryDuration, 'ms');
      
      console.log('✅ Payment Method Query Results:');
      console.log('📊 Total Records Found:', result.recordset.length);
      
      // Log detailed results for debugging
      result.recordset.forEach((record, index) => {
        console.log(`💰 Payment Method ${index + 1}:`, {
          paymentMode: record.paymentMode,
          totalRevenue: record.totalRevenue,
          totalParkingFee: record.totalParkingFee,
          transactionCount: record.transactionCount,
          avgParkingFee: record.avgParkingFee,
          minRevenue: record.minRevenue,
          maxRevenue: record.maxRevenue,
          // Type checking
          revenueType: typeof record.totalRevenue,
          revenueValue: record.totalRevenue,
          revenueAsNumber: Number(record.totalRevenue),
          revenueAsFloat: parseFloat(record.totalRevenue)
        });
      });
      
      // Process results for response (remove debug fields)
      const processedResults = result.recordset.map(record => ({
        paymentMode: record.paymentMode,
        totalRevenue: record.totalRevenue,
        transactionCount: record.transactionCount
      }));
      
      console.log('📤 Processed Results for Response:', processedResults);
      
      // Cache the results
      const cacheTTL = getCacheTTL(dateRange);
      console.log('💾 Caching results with TTL:', cacheTTL, 'seconds');
      await redisService.cacheRevenueByPayment(userId, role, filters, processedResults);
      
      return res.status(200).json({
        success: true,
        data: processedResults,
        cached: false,
        message: 'Revenue by payment method retrieved successfully'
      });
    } catch (error) {
      console.error('❌ Error in getRevenueByPaymentMethod:', error.message);
      console.error('🔍 Error Stack:', error.stack);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by payment method',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET RECENT TRANSACTIONS
   * ===============================================================================
   *
   * Returns the most recent transactions based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with recent transactions
   */
  getRecentTransactions: async (req, res) => {
    try {
      const { limit = 5, companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Base query parameters
      const queryParams = { 
        limit: parseInt(limit) 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute recent transactions query
      const recentTransactionsQuery = `
        SELECT TOP(@limit)
          t.PakringDataID,
          t.PlazaName,
          t.VehicleNumber,
          t.EntryDateTime,
          t.ExitDateTime,
          t.EntryLane,
          t.ExitLane,
          t.ParkedDuration,
          t.ParkingFee,
          t.iTotalGSTFee,
          t.PaymentMode,
          t.PaymentType
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime IS NOT NULL
        ${companyFilter}
        ${plazaFilter}
        ORDER BY t.ExitDateTime DESC
      `;
      
      const result = await db.query(recentTransactionsQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Recent transactions retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRecentTransactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve recent transactions',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET PEAK HOURS DATA
   * ===============================================================================
   *
   * Returns transaction counts by hour of day to identify peak hours.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with hourly transaction data
   */
  getPeakHoursData: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate: formatDateForSQL(startDate), 
        endDate: formatDateForSQL(endDate)
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute peak hours query
      const peakHoursQuery = `
        SELECT
          DATEPART(HOUR, t.ExitDateTime) as hour,
          COUNT(*) as count
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY DATEPART(HOUR, t.ExitDateTime)
        ORDER BY hour
      `;
      
      const result = await db.query(peakHoursQuery, queryParams);
      
      // Fill in missing hours with zero counts
      const hourlyData = Array(24).fill().map((_, i) => ({
        hour: i,
        count: 0
      }));
      
      result.recordset.forEach(row => {
        hourlyData[row.hour].count = row.count;
      });
      
      return res.status(200).json({
        success: true,
        data: hourlyData,
        message: 'Peak hours data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getPeakHoursData:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve peak hours data',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET DAILY REVENUE DATA - Trend Analytics
   * ===============================================================================
   *
   * **Revenue trend analytics endpoint** that provides day-wise revenue breakdown
   * for time-series charts and trend analysis. Optimized for dashboard line charts
   * and revenue trend visualization.
   *
   * ### Request Parameters:
   * - `dateRange` (string): Default 'week', supports 'today', 'yesterday', 'week', 'month', 'year'
   * - `companyId` (optional): Filter by specific company
   * - `plazaId` (optional): Filter by specific plaza
   *
   * ### Response Data Structure:
   * ```json
   * {
   *   "success": true,
   *   "data": [
   *     {
   *       "date": "2024-01-15T00:00:00.000Z",
   *       "revenue": 12500.75,
   *       "transactions": 185,
   *       "label": "Jan 15"
   *     },
   *     {
   *       "date": "2024-01-16T00:00:00.000Z",
   *       "revenue": 15200.25,
   *       "transactions": 220,
   *       "label": "Jan 16"
   *     }
   *   ],
   *   "message": "Daily revenue data retrieved successfully"
   * }
   * ```
   *
   * ### Key Features:
   * - **Daily Aggregation**: Revenue grouped by CAST(ExitDateTime AS DATE)
   * - **Multiple Metrics**: Total revenue and transaction count
   * - **Chart-Ready Format**: Includes formatted labels for frontend chart libraries
   * - **Trend Analysis**: Ordered chronologically for time-series visualization
   *
   * ### Data Calculations:
   * - **Total Revenue**: SUM(ParkingFee) per day (GST excluded)
   * - **Transaction Count**: COUNT(*) of completed transactions per day
   * - **Date Labels**: Formatted as "Jan 15" for chart display
   *
   * ### Performance Optimizations:
   * - **Date Casting**: Efficient DATE casting for daily grouping
   * - **Index Usage**: Optimized for ExitDateTime index
   * - **NOLOCK Hints**: Prevents read blocking on high-traffic tables
   * - **MAXDOP 2**: Controlled parallelism for consistent performance
   *
   * ### Role-based Filtering:
   * - **CompanyAdmin**: Filtered via UserCompany relationship
   * - **PlazaManager**: Filtered via UserPlaza relationship
   * - **SuperAdmin**: No filtering, full system access
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with daily revenue trend data
   */
  getDailyRevenueData: async (req, res) => {
    try {
      console.log('🚀 Daily Revenue Data Request Started');
      const { dateRange = 'week', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      console.log('👤 User Info:', { userId, role });
      console.log('🔧 Request Filters:', { dateRange, companyId, plazaId });

      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);

      // Limit date range for large queries to improve performance (same as Dashboard Summary)
      let optimizedStartDate = startDate;
      let optimizedEndDate = endDate;
      
      // If date range is more than 90 days, limit to 90 days for better performance
      const maxRangeDays = 90; // 3 months max
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        // Create new date for optimization, maintaining the 6:00 AM time
        const optimizedEndDateStr = optimizedEndDate.toISOString().substring(0, 10);
        const tempStartDate = new Date(optimizedEndDateStr + 'T00:00:00');
        tempStartDate.setDate(tempStartDate.getDate() - maxRangeDays);
        const startDateStr = tempStartDate.toISOString().substring(0, 10);
        optimizedStartDate = new Date(startDateStr + 'T06:00:00');
        
        console.log('⚠️ Daily Revenue Date range optimized for performance:');
        console.log('  - Requested range:', requestedRangeDays, 'days');
        console.log('  - Optimized to:', maxRangeDays, 'days');
        console.log('  - Original start:', formatDateForSQL(startDate));
        console.log('  - Optimized start:', formatDateForSQL(optimizedStartDate));
      }

      // Base query parameters
      const queryParams = {
        startDate: formatDateForSQL(optimizedStartDate),
        endDate: formatDateForSQL(optimizedEndDate)
      };

      // Build WHERE clause based on user role and filters
      let whereClause = 'WHERE t.ExitDateTime BETWEEN @startDate AND @endDate';

      // Apply role-based filtering
      if (role === 'CompanyAdmin') {
        whereClause += ' AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        whereClause += ' AND p.Id IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }

      // Apply additional filters
      if (companyId) {
        whereClause += ' AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }

      if (plazaId) {
        whereClause += ' AND p.Id = @plazaId';
        queryParams.plazaId = plazaId;
      }

      console.log('📅 Daily Revenue Date Range Parameters:');
      console.log('  - Start Date (SQL):', queryParams.startDate);
      console.log('  - End Date (SQL):', queryParams.endDate);
      console.log('  - Start Date (JS Original):', startDate);
      console.log('  - End Date (JS Original):', endDate);
      console.log('  - Start Date (JS Optimized):', optimizedStartDate);
      console.log('  - End Date (JS Optimized):', optimizedEndDate);
      console.log('  - Date Range Days:', requestedRangeDays);
      console.log('  - Using 6:00 AM to 6:00 AM operational day logic');
      
      console.log('🔐 Applying Role-based Filtering:');
      if (role === 'CompanyAdmin') {
        console.log('  - CompanyAdmin filter applied for userId:', userId);
      } else if (role === 'PlazaManager') {
        console.log('  - PlazaManager filter applied for userId:', userId);
      } else {
        console.log('  - SuperAdmin: No role-based filtering');
      }
      
      console.log('🏢 Applying Entity-specific Filtering:');
      if (companyId) {
        console.log('  - Company filter applied for companyId:', companyId);
      }
      if (plazaId) {
        console.log('  - Plaza filter applied for plazaId:', plazaId);
      }
      
      console.log('📋 Final Query Parameters for Daily Revenue:', queryParams);
      console.log('📋 WHERE Clause Applied:', whereClause);

      // Execute daily revenue query with enhanced debugging
      const dailyRevenueQuery = `
        SELECT
          CAST(t.ExitDateTime AS DATE) as date,
          -- Revenue Calculation: ParkingFee only (no GST)
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as revenue,
          -- Individual components for debugging
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalParkingFee,
          COUNT(*) as transactions,
          -- Additional debugging info
          AVG(ISNULL(t.ParkingFee, 0)) as avgParkingFee,
          MIN(ISNULL(t.ParkingFee, 0)) as minRevenue,
          MAX(ISNULL(t.ParkingFee, 0)) as maxRevenue
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyId || role === 'CompanyAdmin' || role === 'PlazaManager' ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        ${whereClause}
        GROUP BY CAST(t.ExitDateTime AS DATE)
        ORDER BY date ASC
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;

      console.log('🎯 Executing Daily Revenue Query:');
      console.log('📝 SQL Query Template:', dailyRevenueQuery);
      
      // Generate the exact query with parameters substituted for debugging
      const exactDailyQuery = generateExactQuery(dailyRevenueQuery, queryParams);
      
      console.log('🔍 EXACT DAILY REVENUE SQL QUERY WITH VALUES:');
      console.log('=============================================');
      console.log(exactDailyQuery);
      console.log('=============================================');
      console.log('📋 COPY-PASTE READY FOR SSMS:');
      console.log('------------------------------');
      console.log(exactDailyQuery.replace(/\s+/g, ' ').trim());
      console.log('------------------------------');
      
      // Also log parameters separately for clarity
      console.log('📋 Daily Revenue Query Parameters:');
      Object.keys(queryParams).forEach(key => {
        console.log(`  @${key} = ${queryParams[key]} (${typeof queryParams[key]})`);
      });

      // Log query execution timing
      const queryStartTime = Date.now();
      console.log('⏱️ Daily Revenue Query execution started at:', new Date().toISOString());

      // Execute the daily revenue query
      const result = await db.query(dailyRevenueQuery, queryParams);

      const queryEndTime = Date.now();
      const queryDuration = queryEndTime - queryStartTime;
      console.log('⏱️ Daily Revenue Query execution completed at:', new Date().toISOString());
      console.log('⏱️ Daily Revenue Query execution time:', queryDuration, 'ms');

      console.log('✅ Daily Revenue Query Results:');
      console.log('📊 Total Days Found:', result.recordset.length);

      // Log detailed results for debugging
      result.recordset.forEach((record, index) => {
        console.log(`📅 Day ${index + 1} - ${record.date}:`, {
          date: record.date,
          revenue: record.revenue,
          totalParkingFee: record.totalParkingFee,
          transactions: record.transactions,
          avgParkingFee: record.avgParkingFee,
          minRevenue: record.minRevenue,
          maxRevenue: record.maxRevenue,
          // Type checking
          revenueType: typeof record.revenue,
          revenueValue: record.revenue,
          revenueAsNumber: Number(record.revenue),
          revenueAsFloat: parseFloat(record.revenue)
        });
      });

      // Format the data for the chart
      const formattedData = result.recordset.map((row, index) => {
        const formattedRow = {
          date: row.date,
          revenue: parseFloat(row.revenue) || 0,
          transactions: parseInt(row.transactions) || 0,
          // Format date for display
          label: new Date(row.date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
          })
        };
        
        console.log(`🔄 Formatted Row ${index + 1}:`, {
          original: {
            revenue: row.revenue,
            transactions: row.transactions
          },
          formatted: formattedRow,
          transformations: {
            revenueTransform: `${row.revenue} -> ${formattedRow.revenue}`,
            transactionsTransform: `${row.transactions} -> ${formattedRow.transactions}`
          }
        });
        
        return formattedRow;
      });

      console.log('📤 Final Formatted Data for Response:', formattedData);

      return res.status(200).json({
        success: true,
        data: formattedData,
        message: 'Daily revenue data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getDailyRevenueData:', error.message);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve daily revenue data',
        error: error.message,
        errorCode: error.code
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PLAZA
   * ===============================================================================
   *
   * Returns revenue breakdown by plaza with transaction counts.
   * Only accessible to SuperAdmin and CompanyAdmin roles.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with plaza revenue data
   */
  getRevenueByPlaza: async (req, res) => {
    try {
      const { dateRange = 'today', companyId } = req.query;
      const { id: userId, role } = req.user;
      
      // Only SuperAdmin and CompanyAdmin can access this endpoint
      if (role !== 'SuperAdmin' && role !== 'CompanyAdmin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this resource'
        });
      }
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate: formatDateForSQL(startDate), 
        endDate: formatDateForSQL(endDate)
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      // Execute plaza revenue query
      const plazaRevenueQuery = `
        SELECT
          t.PlazaName,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        GROUP BY t.PlazaName
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(plazaRevenueQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Revenue by plaza retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPlaza:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by plaza',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE STATUS
   * ===============================================================================
   *
   * Returns the current status of lanes for a specific plaza.
   * Primarily used by PlazaManager role.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane status data
   */
  getLaneStatus: async (req, res) => {
    try {
      const { plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      if (!plazaId) {
        return res.status(400).json({
          success: false,
          message: 'Plaza ID is required'
        });
      }
      
      // Check if user has access to this plaza
      if (role !== 'SuperAdmin') {
        let accessQuery;
        
        if (role === 'CompanyAdmin') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
          `;
        } else if (role === 'PlazaManager') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE PlazaId = @plazaId AND UserId = @userId AND IsActive = 1
          `;
        }
        
        const accessResult = await db.query(accessQuery, {
          plazaId,
          userId
        });
        
        if (accessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'You do not have access to this plaza'
          });
        }
      }
      
      // Get lane status
      const laneStatusQuery = `
        SELECT
          l.LaneID,
          l.LaneNumber,
          l.LaneType,
          l.ActiveStatus,
          CASE 
            WHEN l.ActiveStatus = 1 THEN 'Active'
            ELSE 'Inactive'
          END as Status,
          (
            SELECT COUNT(*)
            FROM tblParkwiz_Parking_Data t
            WHERE (t.EntryLane = l.LaneNumber OR t.ExitLane = l.LaneNumber)
            AND t.PlazaCode = p.PlazaCode
            AND t.ExitDateTime >= DATEADD(HOUR, -24, GETDATE())
          ) as TransactionsLast24Hours
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.PlazaID = @plazaId
        ORDER BY l.LaneNumber
      `;
      
      const result = await db.query(laneStatusQuery, { plazaId });
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Lane status retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLaneStatus:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve lane status',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## DEBUG QUERY ENDPOINT
   * ===============================================================================
   * 
   * Debug endpoint to test the exact query and compare with SSMS results
   */
  debugQuery: async (req, res) => {
    try {
      const { dateRange = '2025-06-30', companyId = '11', plazaId } = req.query;
      
      console.log('🐛 DEBUG QUERY STARTED');
      console.log('Parameters:', { dateRange, companyId, plazaId });
      
      const { startDate, endDate } = calculateDateRange(dateRange);
      const queryParams = {
        startDate: formatDateForSQL(startDate),
        endDate: formatDateForSQL(endDate)
      };
      
      if (companyId) queryParams.companyId = companyId;
      if (plazaId) queryParams.plazaId = plazaId;
      
      // Build filters
      let companyFilter = '';
      let plazaFilter = '';
      
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
      }
      
      const debugQuery = `
        SELECT
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        ${companyFilter}
        ${plazaFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      console.log('🎯 DEBUG QUERY:');
      console.log(debugQuery);
      console.log('📊 Parameters:', queryParams);
      
      const result = await db.query(debugQuery, queryParams);
      const data = result.recordset[0];
      
      console.log('✅ DEBUG QUERY RESULT:', data);
      
      return res.json({
        success: true,
        query: debugQuery,
        parameters: queryParams,
        result: data,
        message: 'Debug query executed successfully'
      });
      
    } catch (error) {
      console.error('❌ Debug Query Error:', error);
      return res.status(500).json({
        success: false,
        error: error.message,
        message: 'Debug query failed'
      });
    }
  }
};

module.exports = dashboardController;