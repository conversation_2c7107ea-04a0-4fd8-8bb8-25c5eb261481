{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^6.1.5", "@mui/material": "^6.1.5", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-query": "^5.60.5", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "lucide-react": "^0.456.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.27.0", "react-scripts": "5.0.1", "tailwind-merge": "^3.2.0", "web-vitals": "^2.1.4", "zod": "^3.24.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14"}}