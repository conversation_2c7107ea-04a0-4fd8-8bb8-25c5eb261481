// src/App.js
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/authContext';
import { ThemeProvider } from './contexts/themeContext';
import { ToastProvider } from './contexts/ToastContext';
import ToastContainer from './components/Toast/ToastContainer';
import GlobalThemeStyles from './components/layout/GlobalThemeStyles';
import LoginForm from './components/auth/LoginForm';
import RegisterForm from './components/auth/RegisterForm';
import BranchRegistration from './components/Branch/BranchRegistration';
import { AddressList } from './components/Address/AddressList';
import DashboardLayout from './pages/Dashboard/DashboardLayout';
import DashboardHome from './pages/Dashboard/DashboardHome';
import ManageCountry from './pages/ManageCountry';
import ManageState from './pages/ManageState';
import ManageCity from './pages/ManageCity';
import ManageCompany from './pages/ManageCompany';
import ManagePlaza from './pages/ManagePlaza';
import ManageLane from './pages/ManageLane';
import ManageDigitalPay from './pages/ManageDigitalPay';
import ManageFastag from './pages/ManageFastag';
import ManageAnpr from './pages/ManageAnpr';
import ManageUHFReader from './pages/ManageUHFReader';
import ManagePassRegistration from './pages/ManagePassRegistration';
import ManageUser from './pages/ManageUser';
import UserProfile from './pages/UserProfile';
import ChangePassword from './pages/ChangePassword';
import ProtectedRoute from './components/auth/ProtectedRoute';
import UnauthorizedPage from './components/auth/UnauthorizedPage';
import './App.css';

const App = () => {
  return (
    <ThemeProvider> {/* Wrap everything with ThemeProvider */}
      <GlobalThemeStyles /> {/* Apply global theme styles */}
      <ToastProvider> {/* Wrap everything with ToastProvider */}
        <AuthProvider> {/* Wrap everything with AuthProvider */}
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<LoginForm />} />
          <Route path="/register" element={<RegisterForm />} />
          <Route path="/register/branch-head" element={<BranchRegistration />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />

          {/* Protected Dashboard Routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          }>
            {/* Dashboard Home */}
            <Route index element={<DashboardHome />} />

          {/* Configuration Module */}
          <Route path="manage-country" element={
            <ProtectedRoute requiredModule="Countries" requiredPermissions={["View"]}>
              <ManageCountry />
            </ProtectedRoute>
          } />
          <Route path="manage-state" element={
            <ProtectedRoute requiredModule="States" requiredPermissions={["View"]}>
              <ManageState />
            </ProtectedRoute>
          } />
          <Route path="manage-city" element={
            <ProtectedRoute requiredModule="Cities" requiredPermissions={["View"]}>
              <ManageCity />
            </ProtectedRoute>
          } />

          {/* Company Management Module */}
          <Route path="manage-company" element={
            <ProtectedRoute requiredModule="Companies" requiredPermissions={["View"]}>
              <ManageCompany />
            </ProtectedRoute>
          } />

          {/* Plaza Management Module */}
          <Route path="manage-plaza" element={
            <ProtectedRoute requiredModule="Plazas" requiredPermissions={["View"]}>
              <ManagePlaza />
            </ProtectedRoute>
          } />

          {/* Lane Management Module */}
          <Route path="manage-lane" element={
            <ProtectedRoute requiredModule="Lanes" requiredPermissions={["View"]}>
              <ManageLane />
            </ProtectedRoute>
          } />

          {/* Payment Management Module */}
          <Route path="manage-digital-pay" element={
            <ProtectedRoute requiredModule="Digital Pay" requiredPermissions={["View"]}>
              <ManageDigitalPay />
            </ProtectedRoute>
          } />
          <Route path="manage-fastag" element={
            <ProtectedRoute requiredModule="Fastag" requiredPermissions={["View"]}>
              <ManageFastag />
            </ProtectedRoute>
          } />

          {/* ANPR Management */}
          <Route path="manage-anpr" element={
            <ProtectedRoute requiredModule="ANPR" requiredPermissions={["View"]}>
              <ManageAnpr />
            </ProtectedRoute>
          } />

          {/* UHF Reader Management */}
          <Route path="manage-uhf-reader" element={
            <ProtectedRoute requiredModule="UHF Reader" requiredPermissions={["View"]}>
              <ManageUHFReader />
            </ProtectedRoute>
          } />

          {/* Pass Registration Management */}
          <Route path="manage-pass-registration" element={
            <ProtectedRoute requiredModule="Pass Registration" requiredPermissions={["View"]}>
              <ManagePassRegistration />
            </ProtectedRoute>
          } />

          {/* Address Management */}
          <Route path="manage-address" element={
            <ProtectedRoute requiredModule="Addresses" requiredPermissions={["View"]}>
              <AddressList />
            </ProtectedRoute>
          } />

          {/* User Management */}
          <Route path="manage-users" element={
            <ProtectedRoute requiredModule="Users" requiredPermissions={["View"]}>
              <ManageUser />
            </ProtectedRoute>
          } />

          {/* Reports Module */}
          <Route path="reports" element={
            <ProtectedRoute requiredModule="Reports" requiredPermissions={["View"]}>
              <div>Reports</div>
            </ProtectedRoute>
          } />
        </Route>

        {/* User Profile Routes */}
        <Route path="/profile" element={
          <ProtectedRoute>
            <UserProfile />
          </ProtectedRoute>
        } />

        <Route path="/change-password" element={
          <ProtectedRoute>
            <ChangePassword />
          </ProtectedRoute>
        } />

        {/* Redirect root path to dashboard */}
        <Route path="/" element={<Navigate to="/dashboard" />} />
      </Routes>
      <ToastContainer /> {/* Toast notifications container */}
      </AuthProvider>
      </ToastProvider>
    </ThemeProvider>
  );
};

export default App;
