import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { laneDigitalPayConfigApi } from '../api/laneDigitalPayConfigApi';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import { laneApi } from '../api/laneApi';
import LaneDigitalPayConfigList from '../components/LaneDigitalPayConfig/LaneDigitalPayConfigList';
import LaneDigitalPayConfigDialog from '../components/LaneDigitalPayConfig/LaneDigitalPayConfigDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import ModuleFilters from '../components/filters/ModuleFilters';

export default function ManageDigitalPay() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // Fetch configurations data
  const { data: configurations, isLoading: configurationsLoading, refetch: refetchConfigurations } = useQuery({
    queryKey: ['laneDigitalPayConfigs'],
    queryFn: async () => {
      // Add a timestamp to force a fresh request
      const timestamp = new Date().getTime();
      const data = await laneDigitalPayConfigApi.getAllConfigurations(timestamp);

      // Log the first configuration for debugging
      if (data && data.length > 0) {
        console.log('Digital Pay - Sample config from API:', {
          ConfigLaneID: data[0].ConfigLaneID,
          EnableCardPayment: data[0].EnableCardPayment,
          EnableUPIPhonePe: data[0].EnableUPIPhonePe,
          EnableSendSMS: data[0].EnableSendSMS,
          ActiveStatus: data[0].ActiveStatus
        });
      }

      console.log('Fetched configurations:', data);
      return data;
    },
    // Disable caching to ensure fresh data
    staleTime: 0,
    cacheTime: 0,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  // Fetch plazas for dropdown
  const { data: plazas, isLoading: plazasLoading } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    select: (data) => {
      // Handle the specific structure: {success: true, data: Array(2)}
      if (data && data.success === true && Array.isArray(data.data)) {
        return data.data;
      }

      // Handle other possible data structures
      if (data && Array.isArray(data)) {
        return data;
      } else if (data && data.plazas && Array.isArray(data.plazas)) {
        return data.plazas;
      } else if (data && typeof data === 'object') {
        // If it's an object but not an array, try to extract values
        const plazaArray = Object.values(data).filter(item =>
          item && typeof item === 'object' && (item.Id || item.id) && (item.PlazaName || item.plazaName || item.name)
        );
        if (plazaArray.length > 0) {
          return plazaArray;
        }
      }

      // Default fallback
      return [];
    }
  });

  // Fetch companies for dropdown
  const { data: companies, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => {
      return Array.isArray(data) ? data : [];
    }
  });

  // Fetch lanes for dropdown
  const { data: lanesData, isLoading: lanesLoading } = useQuery({
    queryKey: ['lanes'],
    queryFn: laneApi.getAllLanes,
  });

  // Process raw data - ensure we have arrays
  const rawConfigurations = Array.isArray(configurations) ? configurations : [];
  const rawLanes = Array.isArray(lanesData) ? lanesData : [];
  const lanes = rawLanes; // Define lanes variable for use in the dialog
  const processedPlazas = Array.isArray(plazas) ? plazas : [];
  const processedCompanies = Array.isArray(companies) ? companies : [];

  // Log all configurations including inactive ones
  console.log('Digital Pay configurations before filtering:', rawConfigurations);

  // Log inactive configurations specifically
  const inactiveConfigs = rawConfigurations.filter(config =>
    config.ActiveStatus === '0' ||
    config.ActiveStatus === 0 ||
    config.ActiveStatus === 'N' ||
    config.ActiveStatus === false ||
    config.ActiveStatus === 'false'
  );
  console.log('Digital Pay inactive configurations:', inactiveConfigs);

  // Apply filtering to digital pay configurations data using the new module filter hook
  const {
    filteredData: filteredConfigurations,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: rawConfigurations,
    companies: processedCompanies,
    plazas: processedPlazas,
    lanes: rawLanes,
    companyIdField: 'CompanyID',
    plazaIdField: 'PlazaID',
    laneIdField: 'LaneID',
    module: 'Digital Pay'
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: laneDigitalPayConfigApi.createConfiguration,
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['laneDigitalPayConfigs'] });

      // Get plaza and lane details for better user feedback
      const plazaName = plazas?.find(p => p.Id === variables.PlazaID)?.PlazaName || '';
      const laneNumber = lanes?.find(l => l.LaneID === variables.LaneID)?.LaneNumber || variables.LaneNumber || '';
      const configName = `${plazaName} - Lane ${laneNumber}`;

      toast.success(`Digital pay configuration for ${configName} created successfully`, {
        duration: 4000,
        icon: '✅'
      });

      setDialogOpen(false);
    },
    onError: (error) => toast.showError(`Failed to create configuration: ${error.message}`, {
      duration: 5000,
      icon: '❌'
    }),
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => laneDigitalPayConfigApi.updateConfiguration(id, data),
    onSuccess: (result, variables) => {
      // Force a refetch to ensure we get the latest data
      queryClient.invalidateQueries({ queryKey: ['laneDigitalPayConfigs'] });

      // Get configuration details for better user feedback
      const configName = editingConfig
        ? `${editingConfig.PlazaName || ''} - Lane ${editingConfig.LaneNumberDetail || editingConfig.LaneNumber || 'Unknown'}`
        : 'Configuration';

      // Show success toast with icon
      toast.success(`Digital pay configuration for ${configName} updated successfully`, {
        duration: 4000,
        icon: '✅'
      });

      // Add a small delay to ensure the backend has processed the update
      setTimeout(() => {
        // Force refetch the data using the direct refetch function
        refetchConfigurations();
      }, 1000);

      setDialogOpen(false);
      setEditingConfig(null);
    },
    onError: (error) => toast.showError(`Failed to update configuration: ${error.message}`, {
      duration: 5000,
      icon: '❌'
    }),
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: laneDigitalPayConfigApi.deleteConfiguration,
    // The success and error handling is now in the handleDelete function
  });

  // Toggle status mutation with force refresh
  const toggleStatusMutation = useMutation({
    mutationFn: (configId) => {
      return laneDigitalPayConfigApi.toggleConfigurationStatus(configId, user?.id || 'admin');
    },
    onSuccess: (data) => {
      // Find the configuration to get its name
      const config = rawConfigurations.find(c => {
        const configId = typeof c.ConfigLaneID === 'string'
          ? parseInt(c.ConfigLaneID, 10)
          : c.ConfigLaneID;

        const targetId = typeof data.configId === 'string'
          ? parseInt(data.configId, 10)
          : data.configId;

        return configId === targetId;
      });

      const configName = config
        ? `${config.PlazaName || ''} - Lane ${config.LaneNumberDetail || config.LaneNumber || 'Unknown'}`
        : 'Configuration';

      // Show success toast immediately with icon
      toast.success(`Digital pay configuration for ${configName} ${data.newStatus === '1' ? 'activated' : 'deactivated'} successfully`, {
        duration: 4000,
        icon: data.newStatus === '1' ? '✅' : '🔴'
      });

      // Update the configuration in the query cache immediately
      queryClient.setQueryData(['laneDigitalPayConfigs'], (oldData) => {
        if (!oldData || !Array.isArray(oldData)) return oldData;

        return oldData.map(config => {
          // Convert ConfigLaneID to number for comparison if needed
          const configId = typeof config.ConfigLaneID === 'string'
            ? parseInt(config.ConfigLaneID, 10)
            : config.ConfigLaneID;

          const targetId = typeof data.configId === 'string'
            ? parseInt(data.configId, 10)
            : data.configId;

          if (configId === targetId) {
            // Update the ActiveStatus with the new value
            return {
              ...config,
              ActiveStatus: data.newStatus // Use the normalized value ('1'/'0') from the response
            };
          }
          return config;
        });
      });

      // Force a complete refresh of the configurations data
      queryClient.invalidateQueries({ queryKey: ['laneDigitalPayConfigs'] });

      // Force refetch the data after a short delay
      setTimeout(() => {
        refetchConfigurations();
      }, 500);
    },
    onError: (error) => {
      toast.showError(`Failed to update configuration status: ${error.message || 'Unknown error'}`, {
        duration: 5000,
        icon: '❌'
      });
    },
  });

  const handleSubmit = (data) => {
    // Helper function to ensure values are in '1'/'0' format
    const ensureCorrectFormat = (value) => {
      // If value is already '1' or '0', return it as is
      if (value === '1' || value === '0') return value;

      // If value is 'Y' or 'N', convert to '1' or '0'
      if (value === 'Y') return '1';
      if (value === 'N') return '0';

      // Convert other formats to '1' or '0'
      return value === true || value === 'true' || value === 'y' || value === 1 ? '1' : '0';
    };

    // Ensure toggle fields are properly formatted as '1'/'0' for the API
    const processedData = {
      ...data,
      EnableCardPayment: ensureCorrectFormat(data.EnableCardPayment),
      EnableUPIPhonePe: ensureCorrectFormat(data.EnableUPIPhonePe),
      EnableSendSMS: ensureCorrectFormat(data.EnableSendSMS),
      ActiveStatus: ensureCorrectFormat(data.ActiveStatus),
      AllowBlacklistedVehicle: ensureCorrectFormat(data.AllowBlacklistedVehicle),
      // Add user tracking
      UpdatedBy: user?.id || 'admin'
    };

    // Get configuration details for better user feedback
    const plazaName = plazas?.find(p => p.Id === data.PlazaID)?.PlazaName || '';
    const laneNumber = lanes?.find(l => l.LaneID === data.LaneID)?.LaneNumber || data.LaneNumber || '';
    const configName = `${plazaName} - Lane ${laneNumber}`;

    if (editingConfig) {
      // Check if user has permission to edit this configuration
      if (canEdit(editingConfig, 'Digital Pay')) {
        // Show a loading toast
        const loadingToast = toast.loading(`Updating configuration for ${configName}...`);

        updateMutation.mutate({
          id: editingConfig.ConfigLaneID,
          data: processedData
        }, {
          onSuccess: () => {
            toast.dismiss(loadingToast);
          },
          onError: (error) => {
            toast.dismiss(loadingToast);
            toast.showError(`Failed to update configuration for ${configName}: ${error.message || 'Unknown error'}`, {
              duration: 5000,
              icon: '❌'
            });
          }
        });
      } else {
        toast.showError('You do not have permission to edit this digital payment configuration', {
          icon: '🔒'
        });
      }
    } else {
      // Check if user has permission to create configurations
      if (canCreate('Digital Pay')) {
        // Show a loading toast
        const loadingToast = toast.loading(`Creating configuration for ${configName}...`);

        createMutation.mutate(processedData, {
          onSuccess: () => {
            toast.dismiss(loadingToast);
          },
          onError: (error) => {
            toast.dismiss(loadingToast);
            toast.showError(`Failed to create configuration for ${configName}: ${error.message || 'Unknown error'}`, {
              duration: 5000,
              icon: '❌'
            });
          }
        });
      } else {
        toast.showError('You do not have permission to create digital payment configurations', {
          icon: '🔒'
        });
      }
    }
  };

  const handleEdit = (config) => {
    // Check if user has permission to edit this configuration
    if (!canEdit(config, 'Digital Pay')) {
      toast.showError('You do not have permission to edit this digital payment configuration');
      return;
    }

    console.log('Digital Pay - Editing config ID:', config.ConfigLaneID);
    console.log('Digital Pay - Original config values:', {
      EnableCardPayment: config.EnableCardPayment,
      EnableUPIPhonePe: config.EnableUPIPhonePe,
      EnableSendSMS: config.EnableSendSMS,
      ActiveStatus: config.ActiveStatus,
      AllowBlacklistedVehicle: config.AllowBlacklistedVehicle
    });

    // Helper function to normalize boolean values to 'Y'/'N' format for the form
    const normalizeBoolean = (value) => {
      // If value is already 'Y' or 'N', return it as is
      if (value === 'Y' || value === 'N') return value;

      // If value is '1' or '0', convert to 'Y' or 'N'
      if (value === '1') return 'Y';
      if (value === '0') return 'N';

      // Convert other formats to 'Y' or 'N'
      return value === true || value === 'true' || value === 'y' || value === 1 ? 'Y' : 'N';
    };

    // Ensure toggle fields are properly formatted as 'Y'/'N' for the form
    const formattedConfig = {
      ...config,
      EnableCardPayment: normalizeBoolean(config.EnableCardPayment),
      EnableUPIPhonePe: normalizeBoolean(config.EnableUPIPhonePe),
      EnableSendSMS: normalizeBoolean(config.EnableSendSMS),
      ActiveStatus: normalizeBoolean(config.ActiveStatus),
      AllowBlacklistedVehicle: normalizeBoolean(config.AllowBlacklistedVehicle)
    };

    console.log('Digital Pay - Normalized boolean values for form:', {
      EnableCardPayment: formattedConfig.EnableCardPayment,
      EnableUPIPhonePe: formattedConfig.EnableUPIPhonePe,
      EnableSendSMS: formattedConfig.EnableSendSMS,
      ActiveStatus: formattedConfig.ActiveStatus,
      AllowBlacklistedVehicle: formattedConfig.AllowBlacklistedVehicle
    });

    setEditingConfig(formattedConfig);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    // Find the configuration by ID
    const config = rawConfigurations.find(c => c.ConfigLaneID === id);

    if (!config) {
      toast.showError('Configuration not found');
      return;
    }

    // Get configuration details for better user feedback
    const configName = `${config.PlazaName || ''} - Lane ${config.LaneNumberDetail || config.LaneNumber || 'Unknown'}`;

    // Check if user has permission to delete this configuration
    if (canDelete(config, 'Digital Pay')) {
      if (window.confirm(`Are you sure you want to delete the digital pay configuration for ${configName}?`)) {
        // Show a loading toast
        const loadingToast = toast.loading(`Deleting configuration for ${configName}...`);

        deleteMutation.mutate(id, {
          onSuccess: () => {
            toast.dismiss(loadingToast);
            toast.success(`Digital pay configuration for ${configName} deleted successfully`, {
              duration: 4000,
              icon: '🗑️'
            });

            // Force a complete refresh of the configurations data
            queryClient.invalidateQueries({ queryKey: ['laneDigitalPayConfigs'] });

            // Add a delay to ensure the backend has processed the update
            setTimeout(() => {
              // Force refetch the data using the direct refetch function
              refetchConfigurations();
            }, 1000);
          },
          onError: (error) => {
            toast.dismiss(loadingToast);
            toast.showError(`Failed to delete configuration for ${configName}: ${error.message || 'Unknown error'}`, {
              duration: 5000,
              icon: '❌'
            });
          }
        });
      }
    } else {
      toast.showError('You do not have permission to delete this digital payment configuration', {
        icon: '🔒'
      });
    }
  };

  const handleToggleStatus = (config) => {
    if (!config || !config.ConfigLaneID) {
      toast.showError('Cannot toggle status: Invalid configuration data', {
        icon: '❌'
      });
      return;
    }

    // Get configuration details for better user feedback
    const configName = `${config.PlazaName || ''} - Lane ${config.LaneNumberDetail || config.LaneNumber || 'Unknown'}`;

    // Check if user has permission to edit this configuration
    if (!canEdit(config, 'Digital Pay')) {
      toast.showError('You do not have permission to change the status of this digital payment configuration', {
        icon: '🔒'
      });
      return;
    }

    // Ensure we're passing a valid ID
    let configId;
    try {
      configId = parseInt(config.ConfigLaneID, 10);
      if (isNaN(configId)) {
        throw new Error('Invalid ID format');
      }
    } catch (error) {
      toast.showError('Invalid configuration ID', {
        icon: '❌'
      });
      return;
    }

    // Determine if the configuration is currently active
    const isCurrentlyActive =
      config.ActiveStatus === '1' ||
      config.ActiveStatus === 1 ||
      config.ActiveStatus === true ||
      config.ActiveStatus === 'true' ||
      config.ActiveStatus === 'Y';

    // Show a confirmation dialog
    if (window.confirm(`Are you sure you want to ${isCurrentlyActive ? 'deactivate' : 'activate'} the digital payment configuration for ${configName}?`)) {
      // Show a loading toast
      const loadingToast = toast.loading(`${isCurrentlyActive ? 'Deactivating' : 'Activating'} configuration for ${configName}...`);

      // Pass the configuration ID directly to the mutation with callbacks
      toggleStatusMutation.mutate(configId, {
        onSuccess: (data) => {
          toast.dismiss(loadingToast);
        },
        onError: (error) => {
          toast.dismiss(loadingToast);
          toast.showError(`Failed to ${isCurrentlyActive ? 'deactivate' : 'activate'} configuration for ${configName}: ${error.message || 'Unknown error'}`, {
            duration: 5000,
            icon: '❌'
          });
        }
      });
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingConfig(null);
  };

  if (configurationsLoading || plazasLoading || companiesLoading || lanesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-3xl font-bold text-gray-900 mr-4">Digital Payment Configuration</h1>
            <button
              onClick={() => {
                console.log('Manual refresh triggered');
                refetchConfigurations();
                toast.success('Refreshing digital pay data...');
              }}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-150 ease-in-out"
            >
              Refresh Data
            </button>
          </div>
          <PermissionButton
            requiredModule="Digital Pay"
            requiredPermissions={["Create"]}
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Configuration
          </PermissionButton>
        </div>

        {/* Filter Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <ModuleFilters
              companies={processedCompanies || []}
              plazas={processedPlazas || []}
              lanes={rawLanes || []}
              filters={filters}
              onFilterChange={setFilters}
              showCompanyFilter={true}
              showPlazaFilter={true}
              showLaneFilter={true}
              loading={configurationsLoading || plazasLoading || companiesLoading || lanesLoading}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <LaneDigitalPayConfigList
            configurations={filteredConfigurations || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={(id) => {
              // Fetch the configuration by ID
              laneDigitalPayConfigApi.getConfigurationById(id)
                .then(config => {
                  console.log('Selected config:', config);
                  setSelectedConfig(config);
                })
                .catch(error => {
                  console.error('Error fetching configuration:', error);
                  toast.showError('Failed to fetch configuration details');
                });
            }}
            onToggleStatus={handleToggleStatus}
          />
        </div>

        {plazas && companies && lanes && (
          <LaneDigitalPayConfigDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={editingConfig}
            title={editingConfig ? 'Edit Digital Pay Configuration' : 'Add Digital Pay Configuration'}
            plazas={plazas}
            companies={companies}
            lanes={lanes}
          />
        )}
      </div>
    </div>
  );
}